{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using TensorFlow backend.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[23.619713    0.63145584]\n", "WARNING:tensorflow:From C:\\Users\\<USER>\\Anaconda3\\envs\\tf-gpu\\lib\\site-packages\\tensorflow\\python\\framework\\op_def_library.py:263: colocate_with (from tensorflow.python.framework.ops) is deprecated and will be removed in a future version.\n", "Instructions for updating:\n", "Colocations handled automatically by placer.\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "flatten_1 (<PERSON>ten)          (None, 2)                 0         \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 16)                48        \n", "_________________________________________________________________\n", "activation_1 (Activation)    (None, 16)                0         \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 16)                272       \n", "_________________________________________________________________\n", "activation_2 (Activation)    (None, 16)                0         \n", "_________________________________________________________________\n", "dense_3 (<PERSON><PERSON>)              (None, 16)                272       \n", "_________________________________________________________________\n", "activation_3 (Activation)    (None, 16)                0         \n", "_________________________________________________________________\n", "dense_4 (<PERSON><PERSON>)              (<PERSON>, 2)                 34        \n", "_________________________________________________________________\n", "lambda_1 (Lambda)            (None, 2)                 0         \n", "=================================================================\n", "Total params: 626\n", "Trainable params: 626\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "None\n", "__________________________________________________________________________________________________\n", "Layer (type)                    Output Shape         Param #     Connected to                     \n", "==================================================================================================\n", "observation_input (InputLayer)  (None, 1, 2)         0                                            \n", "__________________________________________________________________________________________________\n", "action_input (InputLayer)       (None, 2)            0                                            \n", "__________________________________________________________________________________________________\n", "flatten_2 (<PERSON><PERSON>)             (None, 2)            0           observation_input[0][0]          \n", "__________________________________________________________________________________________________\n", "concatenate_1 (Concatenate)     (None, 4)            0           action_input[0][0]               \n", "                                                                 flatten_2[0][0]                  \n", "__________________________________________________________________________________________________\n", "dense_5 (<PERSON><PERSON>)                 (None, 32)           160         concatenate_1[0][0]              \n", "__________________________________________________________________________________________________\n", "activation_4 (Activation)       (None, 32)           0           dense_5[0][0]                    \n", "__________________________________________________________________________________________________\n", "dense_6 (<PERSON><PERSON>)                 (None, 32)           1056        activation_4[0][0]               \n", "__________________________________________________________________________________________________\n", "activation_5 (Activation)       (None, 32)           0           dense_6[0][0]                    \n", "__________________________________________________________________________________________________\n", "dense_7 (<PERSON><PERSON>)                 (None, 32)           1056        activation_5[0][0]               \n", "__________________________________________________________________________________________________\n", "activation_6 (Activation)       (None, 32)           0           dense_7[0][0]                    \n", "__________________________________________________________________________________________________\n", "dense_8 (<PERSON><PERSON>)                 (None, 1)            33          activation_6[0][0]               \n", "__________________________________________________________________________________________________\n", "activation_7 (Activation)       (None, 1)            0           dense_8[0][0]                    \n", "==================================================================================================\n", "Total params: 2,305\n", "Trainable params: 2,305\n", "Non-trainable params: 0\n", "__________________________________________________________________________________________________\n", "None\n"]}], "source": ["import sys\n", "import numpy as np\n", "import pandas as pd\n", "from agents.rl_drone import RLAgent\n", "from agents.drone_sim_env import drone_sim\n", "\n", "env = drone_sim()\n", "agent = RLAgent(env)\n", "ENV_NAME = 'drone'\n", "#training_history=agent.agent.fit(env, nb_steps=100000, visualize=True, verbose=1, nb_max_episode_steps=10)\n", "\n", "#After training is done, we save the final weights.\n", "#agent.agent.save_weights('ddpg_{}_weights.h5f'.format(ENV_NAME), overwrite=True)\n", "#agent.agent.load_weights('ddpg_{}_weights.h5f'.format(ENV_NAME))\n", "\n", "# Finally, evaluate our algorithm for 5 episodes.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing for 1 episodes ...\n", "Episode 1: reward: 18.693, steps: 10\n"]}, {"data": {"text/plain": ["<keras.callbacks.History at 0x2817aada860>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "#After training is done, we save the final weights.\n", "\n", "agent.agent.load_weights('ddpg_{}_weights.h5f'.format(ENV_NAME))\n", "agent.agent.test(env, nb_episodes=1, visualize=True, verbose=1, nb_max_episode_steps=10,start_step_policy=1)"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["## Plot the Rewards\n", "\n", "Once you are satisfied with your performance, plot the episode rewards, either from a single run, or averaged over multiple runs. "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x14c949a8160>,\n", " <matplotlib.lines.Line2D at 0x14c949a8278>]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "df=pd.DataFrame()\n", "df = pd.DataFrame(columns=['raw','MA'])\n", "df['raw'] = training_history.history['episode_reward']\n", "df['MA']= df.rolling(window=100).mean()\n", "plt.plot(df)\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-56.646416 -44.977577]\n", "[-57.505188  60.      ]\n"]}], "source": ["print(agent.agent.forward([621, 404]))\n", "print(agent.agent.forward([463, 76]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "TensorFlow-GPU", "language": "python", "name": "tf-gpu"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}