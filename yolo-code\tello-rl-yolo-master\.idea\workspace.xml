<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="3" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1e8ce86a-bde1-4a4a-8203-4abd98854e47" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/agents/drone_sim_env.py" beforeDir="false" afterPath="$PROJECT_DIR$/agents/drone_sim_env.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/agents/train-rl-agent.py" beforeDir="false" afterPath="$PROJECT_DIR$/agents/train-rl-agent.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/example.py" beforeDir="false" afterPath="$PROJECT_DIR$/example.py" afterDir="false" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/example.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="369">
              <caret line="126" column="48" selection-start-line="126" selection-start-column="48" selection-end-line="126" selection-end-column="48" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/agents/rl_drone_sim.py">
          <provider selected="true" editor-type-id="text-editor">
            <state>
              <folding>
                <element signature="e#0#18#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/agents/rl_drone.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="425">
              <caret line="33" column="50" selection-start-line="33" selection-start-column="50" selection-end-line="33" selection-end-column="50" />
              <folding>
                <element signature="e#0#18#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/agents/train-rl-agent.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="85">
              <caret line="6" column="14" selection-start-line="6" selection-start-column="14" selection-end-line="6" selection-end-column="14" />
              <folding>
                <element signature="e#0#35#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/rl/core.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="277">
              <caret line="249" column="64" selection-start-line="249" selection-start-column="64" selection-end-line="249" selection-end-column="64" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/train.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="209">
              <caret line="249" column="22" lean-forward="true" selection-start-line="249" selection-start-column="22" selection-end-line="249" selection-end-column="22" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/tensorflow/python/keras/engine/training.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="328">
              <caret line="756" column="47" lean-forward="true" selection-start-line="756" selection-start-column="47" selection-end-line="756" selection-end-column="47" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/agents/drone_real_env.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="204">
              <caret line="18" column="30" selection-start-line="18" selection-start-column="30" selection-end-line="18" selection-end-column="30" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/agents/drone_sim_env.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1139">
              <caret line="71" column="29" selection-start-line="71" selection-start-column="29" selection-end-line="71" selection-end-column="29" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>fit_generator</find>
      <find>callbacks</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/djitellopy/tello.py" />
        <option value="$PROJECT_DIR$/example_test.py" />
        <option value="$PROJECT_DIR$/utils/bbox.py" />
        <option value="$PROJECT_DIR$/agents/rl_drone.py" />
        <option value="$PROJECT_DIR$/agents/train-rl-agent.py" />
        <option value="$PROJECT_DIR$/example.py" />
        <option value="$PROJECT_DIR$/agents/drone_sim_env.py" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="130" />
    <option name="y" value="170" />
    <option name="width" value="1750" />
    <option name="height" value="980" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="tello-rl-yolo" type="b2602c69:ProjectViewProjectNode" />
              <item name="tello-rl-yolo" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="run.code.analysis.last.selected.profile" value="pProject Default" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PythonContentEntriesConfigurable" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\PycharmProjects\Tello" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Python.example">
    <configuration name="example" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="Tello" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/example.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="example_test" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="Tello" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/example_test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.example" />
        <item itemvalue="Python.example_test" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1e8ce86a-bde1-4a4a-8203-4abd98854e47" name="Default Changelist" comment="" />
      <created>1553560547915</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1553560547915</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="-7" y="-7" width="1550" height="838" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.2247191" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" weight="0.3286119" />
      <window_info active="true" anchor="bottom" id="Run" order="2" visible="true" weight="0.3286119" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.39943343" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Version Control" order="7" />
      <window_info anchor="bottom" id="Terminal" order="8" weight="0.3286119" />
      <window_info anchor="bottom" id="Event Log" order="9" side_tool="true" />
      <window_info anchor="bottom" id="Python Console" order="10" weight="0.3286119" />
      <window_info anchor="bottom" id="Inspection Results" order="11" weight="0.3286119" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="XDebuggerManager">
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="boxes" language="Python" />
        <watch expression="frame" />
        <watch expression="net_h" />
      </configuration>
    </watches-manager>
  </component>
  <component name="debuggerHistoryManager">
    <expressions id="watch">
      <expression>
        <expression-string>boxes</expression-string>
        <language-id>Python</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
    </expressions>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$APPLICATION_HOME_DIR$/helpers/python-skeletons/_pytest/__init__.py">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/utils/image.py">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <folding>
            <element signature="e#0#10#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/djitellopy/decorators.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="194">
          <caret line="33" selection-start-line="33" selection-end-line="33" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/queue.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="82">
          <caret line="175" selection-start-line="175" selection-end-line="175" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/codecs.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="125">
          <caret line="308" selection-start-line="308" selection-end-line="308" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/predict.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1462">
          <caret line="10" column="18" selection-start-line="10" selection-start-column="18" selection-end-line="10" selection-end-column="18" />
          <folding>
            <element signature="e#24#33#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/.PyCharmCE2018.3/system/python_stubs/-**********/cv2/cv2/VideoCapture.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-934">
          <caret line="124" column="51" selection-start-line="124" selection-start-column="51" selection-end-line="124" selection-end-column="51" />
        </state>
      </provider>
    </entry>
    <entry file="file://$APPLICATION_HOME_DIR$/helpers/pydev/_pydev_imps/_pydev_execfile.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="289">
          <caret line="17" selection-start-line="17" selection-end-line="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/example_test.py" />
    <entry file="file://$PROJECT_DIR$/yolo3_one_file_to_detect_them_all.py" />
    <entry file="file://$PROJECT_DIR$/yolo.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-3672">
          <folding>
            <element signature="e#0#106#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/utils/bbox.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="170">
          <caret line="58" column="4" selection-start-line="58" selection-start-column="4" selection-end-line="58" selection-end-column="4" />
          <folding>
            <element signature="e#0#18#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/utils/utils.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2465">
          <caret line="232" column="4" selection-start-line="232" selection-start-column="4" selection-end-line="232" selection-end-column="4" />
          <folding>
            <element signature="e#0#10#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/djitellopy/tello.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="149">
          <caret line="9" column="6" selection-start-line="9" selection-start-column="6" selection-end-line="9" selection-end-column="6" />
          <folding>
            <element signature="e#15#28#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/evaluate.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="188">
          <caret line="12" column="35" lean-forward="true" selection-start-line="12" selection-start-column="35" selection-end-line="12" selection-end-column="35" />
          <folding>
            <element signature="e#24#39#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/zoo/config_voc_person.json" />
    <entry file="file://$PROJECT_DIR$/zoo/config_voc.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="357">
          <caret line="21" column="30" selection-start-line="21" selection-start-column="30" selection-end-line="21" selection-end-column="30" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/generator.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-3303">
          <folding>
            <element signature="e#0#10#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/tensorflow/contrib/tpu/python/tpu/keras_support.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="295">
          <caret line="1454" column="28" lean-forward="true" selection-start-line="1454" selection-start-column="28" selection-end-line="1454" selection-end-column="28" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/agents/rl_drone_sim.py">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <folding>
            <element signature="e#0#18#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/agents/rl_drone.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="425">
          <caret line="33" column="50" selection-start-line="33" selection-start-column="50" selection-end-line="33" selection-end-column="50" />
          <folding>
            <element signature="e#0#18#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/agents/train-rl-agent.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="85">
          <caret line="6" column="14" selection-start-line="6" selection-start-column="14" selection-end-line="6" selection-end-column="14" />
          <folding>
            <element signature="e#0#35#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/agents/drone_real_env.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="204">
          <caret line="18" column="30" selection-start-line="18" selection-start-column="30" selection-end-line="18" selection-end-column="30" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/agents/drone_sim_env.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1139">
          <caret line="71" column="29" selection-start-line="71" selection-start-column="29" selection-end-line="71" selection-end-column="29" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/rl/core.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="277">
          <caret line="249" column="64" selection-start-line="249" selection-start-column="64" selection-end-line="249" selection-end-column="64" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/keras/engine/training.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-3609">
          <caret line="1277" column="8" selection-start-line="1277" selection-start-column="8" selection-end-line="1277" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/train.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="209">
          <caret line="249" column="22" lean-forward="true" selection-start-line="249" selection-start-column="22" selection-end-line="249" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$USER_HOME$/Anaconda3/envs/tf-gpu/Lib/site-packages/tensorflow/python/keras/engine/training.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="328">
          <caret line="756" column="47" lean-forward="true" selection-start-line="756" selection-start-column="47" selection-end-line="756" selection-end-column="47" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/example.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="369">
          <caret line="126" column="48" selection-start-line="126" selection-start-column="48" selection-end-line="126" selection-end-column="48" />
        </state>
      </provider>
    </entry>
  </component>
</project>